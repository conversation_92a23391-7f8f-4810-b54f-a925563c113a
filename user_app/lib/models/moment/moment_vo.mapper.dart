// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, unnecessary_cast, override_on_non_overriding_member
// ignore_for_file: strict_raw_type, inference_failure_on_untyped_parameter

part of 'moment_vo.dart';

class MomentImageVoMapper extends ClassMapperBase<MomentImageVo> {
  MomentImageVoMapper._();

  static MomentImageVoMapper? _instance;
  static MomentImageVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentImageVoMapper._());
    }
    return _instance!;
  }

  @override
  final String id = 'MomentImageVo';

  static int? _$id(MomentImageVo v) => v.id;
  static const Field<MomentImageVo, int> _f$id = Field('id', _$id, opt: true);
  static int? _$momentId(MomentImageVo v) => v.momentId;
  static const Field<MomentImageVo, int> _f$momentId =
      Field('momentId', _$momentId, key: r'moment_id', opt: true);
  static String _$imageUrl(MomentImageVo v) => v.imageUrl;
  static const Field<MomentImageVo, String> _f$imageUrl =
      Field('imageUrl', _$imageUrl, key: r'image_url');
  static int? _$displayOrder(MomentImageVo v) => v.displayOrder;
  static const Field<MomentImageVo, int> _f$displayOrder =
      Field('displayOrder', _$displayOrder, key: r'display_order', opt: true);

  @override
  final MappableFields<MomentImageVo> fields = const {
    #id: _f$id,
    #momentId: _f$momentId,
    #imageUrl: _f$imageUrl,
    #displayOrder: _f$displayOrder,
  };

  static MomentImageVo _instantiate(DecodingData data) {
    return MomentImageVo(
        id: data.dec(_f$id),
        momentId: data.dec(_f$momentId),
        imageUrl: data.dec(_f$imageUrl),
        displayOrder: data.dec(_f$displayOrder));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentImageVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentImageVo>(map);
  }

  static MomentImageVo fromJson(String json) {
    return ensureInitialized().decodeJson<MomentImageVo>(json);
  }
}

mixin MomentImageVoMappable {
  String toJson() {
    return MomentImageVoMapper.ensureInitialized()
        .encodeJson<MomentImageVo>(this as MomentImageVo);
  }

  Map<String, dynamic> toMap() {
    return MomentImageVoMapper.ensureInitialized()
        .encodeMap<MomentImageVo>(this as MomentImageVo);
  }

  MomentImageVoCopyWith<MomentImageVo, MomentImageVo, MomentImageVo>
      get copyWith => _MomentImageVoCopyWithImpl<MomentImageVo, MomentImageVo>(
          this as MomentImageVo, $identity, $identity);
  @override
  String toString() {
    return MomentImageVoMapper.ensureInitialized()
        .stringifyValue(this as MomentImageVo);
  }

  @override
  bool operator ==(Object other) {
    return MomentImageVoMapper.ensureInitialized()
        .equalsValue(this as MomentImageVo, other);
  }

  @override
  int get hashCode {
    return MomentImageVoMapper.ensureInitialized()
        .hashValue(this as MomentImageVo);
  }
}

extension MomentImageVoValueCopy<$R, $Out>
    on ObjectCopyWith<$R, MomentImageVo, $Out> {
  MomentImageVoCopyWith<$R, MomentImageVo, $Out> get $asMomentImageVo =>
      $base.as((v, t, t2) => _MomentImageVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentImageVoCopyWith<$R, $In extends MomentImageVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  $R call({int? id, int? momentId, String? imageUrl, int? displayOrder});
  MomentImageVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _MomentImageVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentImageVo, $Out>
    implements MomentImageVoCopyWith<$R, MomentImageVo, $Out> {
  _MomentImageVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentImageVo> $mapper =
      MomentImageVoMapper.ensureInitialized();
  @override
  $R call(
          {Object? id = $none,
          Object? momentId = $none,
          String? imageUrl,
          Object? displayOrder = $none}) =>
      $apply(FieldCopyWithData({
        if (id != $none) #id: id,
        if (momentId != $none) #momentId: momentId,
        if (imageUrl != null) #imageUrl: imageUrl,
        if (displayOrder != $none) #displayOrder: displayOrder
      }));
  @override
  MomentImageVo $make(CopyWithData data) => MomentImageVo(
      id: data.get(#id, or: $value.id),
      momentId: data.get(#momentId, or: $value.momentId),
      imageUrl: data.get(#imageUrl, or: $value.imageUrl),
      displayOrder: data.get(#displayOrder, or: $value.displayOrder));

  @override
  MomentImageVoCopyWith<$R2, MomentImageVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentImageVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}

class MomentVoMapper extends ClassMapperBase<MomentVo> {
  MomentVoMapper._();

  static MomentVoMapper? _instance;
  static MomentVoMapper ensureInitialized() {
    if (_instance == null) {
      MapperContainer.globals.use(_instance = MomentVoMapper._());
      UserMapper.ensureInitialized();
      MomentImageVoMapper.ensureInitialized();
    }
    return _instance!;
  }

  @override
  final String id = 'MomentVo';

  static int? _$id(MomentVo v) => v.id;
  static const Field<MomentVo, int> _f$id = Field('id', _$id, opt: true);
  static User? _$publisher(MomentVo v) => v.publisher;
  static const Field<MomentVo, User> _f$publisher =
      Field('publisher', _$publisher, opt: true);
  static int? _$userId(MomentVo v) => v.userId;
  static const Field<MomentVo, int> _f$userId =
      Field('userId', _$userId, key: r'user_id', opt: true);
  static String? _$userName(MomentVo v) => v.userName;
  static const Field<MomentVo, String> _f$userName =
      Field('userName', _$userName, key: r'user_name', opt: true);
  static String? _$userAvatar(MomentVo v) => v.userAvatar;
  static const Field<MomentVo, String> _f$userAvatar =
      Field('userAvatar', _$userAvatar, key: r'user_avatar', opt: true);
  static String? _$momentType(MomentVo v) => v.momentType;
  static const Field<MomentVo, String> _f$momentType =
      Field('momentType', _$momentType, key: r'moment_type', opt: true);
  static String? _$content(MomentVo v) => v.content;
  static const Field<MomentVo, String> _f$content =
      Field('content', _$content, opt: true);
  static String? _$visibility(MomentVo v) => v.visibility;
  static const Field<MomentVo, String> _f$visibility =
      Field('visibility', _$visibility, opt: true);
  static int? _$fishingSpotId(MomentVo v) => v.fishingSpotId;
  static const Field<MomentVo, int> _f$fishingSpotId = Field(
      'fishingSpotId', _$fishingSpotId,
      key: r'fishing_spot_id', opt: true);
  static String? _$fishingSpotName(MomentVo v) => v.fishingSpotName;
  static const Field<MomentVo, String> _f$fishingSpotName = Field(
      'fishingSpotName', _$fishingSpotName,
      key: r'fishing_spot_name', opt: true);
  static String? _$typeSpecificData(MomentVo v) => v.typeSpecificData;
  static const Field<MomentVo, String> _f$typeSpecificData = Field(
      'typeSpecificData', _$typeSpecificData,
      key: r'type_specific_data', opt: true);
  static List<MomentImageVo>? _$images(MomentVo v) => v.images;
  static const Field<MomentVo, List<MomentImageVo>> _f$images =
      Field('images', _$images, opt: true);
  static int? _$likeCount(MomentVo v) => v.likeCount;
  static const Field<MomentVo, int> _f$likeCount =
      Field('likeCount', _$likeCount, key: r'like_count', opt: true);
  static int? _$commentCount(MomentVo v) => v.commentCount;
  static const Field<MomentVo, int> _f$commentCount =
      Field('commentCount', _$commentCount, key: r'comment_count', opt: true);
  static bool? _$isLiked(MomentVo v) => v.isLiked;
  static const Field<MomentVo, bool> _f$isLiked =
      Field('isLiked', _$isLiked, key: r'is_liked', opt: true);
  static bool? _$isBookmarked(MomentVo v) => v.isBookmarked;
  static const Field<MomentVo, bool> _f$isBookmarked =
      Field('isBookmarked', _$isBookmarked, key: r'is_bookmarked', opt: true);
  static String? _$createdAt(MomentVo v) => v.createdAt;
  static const Field<MomentVo, String> _f$createdAt =
      Field('createdAt', _$createdAt, key: r'created_at', opt: true);
  static String? _$updatedAt(MomentVo v) => v.updatedAt;
  static const Field<MomentVo, String> _f$updatedAt =
      Field('updatedAt', _$updatedAt, key: r'updated_at', opt: true);

  @override
  final MappableFields<MomentVo> fields = const {
    #id: _f$id,
    #publisher: _f$publisher,
    #userId: _f$userId,
    #userName: _f$userName,
    #userAvatar: _f$userAvatar,
    #momentType: _f$momentType,
    #content: _f$content,
    #visibility: _f$visibility,
    #fishingSpotId: _f$fishingSpotId,
    #fishingSpotName: _f$fishingSpotName,
    #typeSpecificData: _f$typeSpecificData,
    #images: _f$images,
    #likeCount: _f$likeCount,
    #commentCount: _f$commentCount,
    #isLiked: _f$isLiked,
    #isBookmarked: _f$isBookmarked,
    #createdAt: _f$createdAt,
    #updatedAt: _f$updatedAt,
  };

  static MomentVo _instantiate(DecodingData data) {
    return MomentVo(
        id: data.dec(_f$id),
        publisher: data.dec(_f$publisher),
        userId: data.dec(_f$userId),
        userName: data.dec(_f$userName),
        userAvatar: data.dec(_f$userAvatar),
        momentType: data.dec(_f$momentType),
        content: data.dec(_f$content),
        visibility: data.dec(_f$visibility),
        fishingSpotId: data.dec(_f$fishingSpotId),
        fishingSpotName: data.dec(_f$fishingSpotName),
        typeSpecificData: data.dec(_f$typeSpecificData),
        images: data.dec(_f$images),
        likeCount: data.dec(_f$likeCount),
        commentCount: data.dec(_f$commentCount),
        isLiked: data.dec(_f$isLiked),
        isBookmarked: data.dec(_f$isBookmarked),
        createdAt: data.dec(_f$createdAt),
        updatedAt: data.dec(_f$updatedAt));
  }

  @override
  final Function instantiate = _instantiate;

  static MomentVo fromMap(Map<String, dynamic> map) {
    return ensureInitialized().decodeMap<MomentVo>(map);
  }

  static MomentVo fromJson(String json) {
    return ensureInitialized().decodeJson<MomentVo>(json);
  }
}

mixin MomentVoMappable {
  String toJson() {
    return MomentVoMapper.ensureInitialized()
        .encodeJson<MomentVo>(this as MomentVo);
  }

  Map<String, dynamic> toMap() {
    return MomentVoMapper.ensureInitialized()
        .encodeMap<MomentVo>(this as MomentVo);
  }

  MomentVoCopyWith<MomentVo, MomentVo, MomentVo> get copyWith =>
      _MomentVoCopyWithImpl<MomentVo, MomentVo>(
          this as MomentVo, $identity, $identity);
  @override
  String toString() {
    return MomentVoMapper.ensureInitialized().stringifyValue(this as MomentVo);
  }

  @override
  bool operator ==(Object other) {
    return MomentVoMapper.ensureInitialized()
        .equalsValue(this as MomentVo, other);
  }

  @override
  int get hashCode {
    return MomentVoMapper.ensureInitialized().hashValue(this as MomentVo);
  }
}

extension MomentVoValueCopy<$R, $Out> on ObjectCopyWith<$R, MomentVo, $Out> {
  MomentVoCopyWith<$R, MomentVo, $Out> get $asMomentVo =>
      $base.as((v, t, t2) => _MomentVoCopyWithImpl<$R, $Out>(v, t, t2));
}

abstract class MomentVoCopyWith<$R, $In extends MomentVo, $Out>
    implements ClassCopyWith<$R, $In, $Out> {
  UserCopyWith<$R, User, User>? get publisher;
  ListCopyWith<$R, MomentImageVo,
      MomentImageVoCopyWith<$R, MomentImageVo, MomentImageVo>>? get images;
  $R call(
      {int? id,
      User? publisher,
      int? userId,
      String? userName,
      String? userAvatar,
      String? momentType,
      String? content,
      String? visibility,
      int? fishingSpotId,
      String? fishingSpotName,
      String? typeSpecificData,
      List<MomentImageVo>? images,
      int? likeCount,
      int? commentCount,
      bool? isLiked,
      bool? isBookmarked,
      String? createdAt,
      String? updatedAt});
  MomentVoCopyWith<$R2, $In, $Out2> $chain<$R2, $Out2>(Then<$Out2, $R2> t);
}

class _MomentVoCopyWithImpl<$R, $Out>
    extends ClassCopyWithBase<$R, MomentVo, $Out>
    implements MomentVoCopyWith<$R, MomentVo, $Out> {
  _MomentVoCopyWithImpl(super.value, super.then, super.then2);

  @override
  late final ClassMapperBase<MomentVo> $mapper =
      MomentVoMapper.ensureInitialized();
  @override
  UserCopyWith<$R, User, User>? get publisher =>
      $value.publisher?.copyWith.$chain((v) => call(publisher: v));
  @override
  ListCopyWith<$R, MomentImageVo,
          MomentImageVoCopyWith<$R, MomentImageVo, MomentImageVo>>?
      get images => $value.images != null
          ? ListCopyWith($value.images!, (v, t) => v.copyWith.$chain(t),
              (v) => call(images: v))
          : null;
  @override
  $R call(
          {Object? id = $none,
          Object? publisher = $none,
          Object? userId = $none,
          Object? userName = $none,
          Object? userAvatar = $none,
          Object? momentType = $none,
          Object? content = $none,
          Object? visibility = $none,
          Object? fishingSpotId = $none,
          Object? fishingSpotName = $none,
          Object? typeSpecificData = $none,
          Object? images = $none,
          Object? likeCount = $none,
          Object? commentCount = $none,
          Object? isLiked = $none,
          Object? isBookmarked = $none,
          Object? createdAt = $none,
          Object? updatedAt = $none}) =>
      $apply(FieldCopyWithData({
        if (id != $none) #id: id,
        if (publisher != $none) #publisher: publisher,
        if (userId != $none) #userId: userId,
        if (userName != $none) #userName: userName,
        if (userAvatar != $none) #userAvatar: userAvatar,
        if (momentType != $none) #momentType: momentType,
        if (content != $none) #content: content,
        if (visibility != $none) #visibility: visibility,
        if (fishingSpotId != $none) #fishingSpotId: fishingSpotId,
        if (fishingSpotName != $none) #fishingSpotName: fishingSpotName,
        if (typeSpecificData != $none) #typeSpecificData: typeSpecificData,
        if (images != $none) #images: images,
        if (likeCount != $none) #likeCount: likeCount,
        if (commentCount != $none) #commentCount: commentCount,
        if (isLiked != $none) #isLiked: isLiked,
        if (isBookmarked != $none) #isBookmarked: isBookmarked,
        if (createdAt != $none) #createdAt: createdAt,
        if (updatedAt != $none) #updatedAt: updatedAt
      }));
  @override
  MomentVo $make(CopyWithData data) => MomentVo(
      id: data.get(#id, or: $value.id),
      publisher: data.get(#publisher, or: $value.publisher),
      userId: data.get(#userId, or: $value.userId),
      userName: data.get(#userName, or: $value.userName),
      userAvatar: data.get(#userAvatar, or: $value.userAvatar),
      momentType: data.get(#momentType, or: $value.momentType),
      content: data.get(#content, or: $value.content),
      visibility: data.get(#visibility, or: $value.visibility),
      fishingSpotId: data.get(#fishingSpotId, or: $value.fishingSpotId),
      fishingSpotName: data.get(#fishingSpotName, or: $value.fishingSpotName),
      typeSpecificData:
          data.get(#typeSpecificData, or: $value.typeSpecificData),
      images: data.get(#images, or: $value.images),
      likeCount: data.get(#likeCount, or: $value.likeCount),
      commentCount: data.get(#commentCount, or: $value.commentCount),
      isLiked: data.get(#isLiked, or: $value.isLiked),
      isBookmarked: data.get(#isBookmarked, or: $value.isBookmarked),
      createdAt: data.get(#createdAt, or: $value.createdAt),
      updatedAt: data.get(#updatedAt, or: $value.updatedAt));

  @override
  MomentVoCopyWith<$R2, MomentVo, $Out2> $chain<$R2, $Out2>(
          Then<$Out2, $R2> t) =>
      _MomentVoCopyWithImpl<$R2, $Out2>($value, $cast, t);
}
