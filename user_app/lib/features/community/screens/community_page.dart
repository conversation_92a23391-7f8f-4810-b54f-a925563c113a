import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/utils/date_time_util.dart';
import 'package:user_app/view_models/auth_view_model.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late CommunityViewModel _communityViewModel;
  late AnimationController _fabAnimationController;

  bool _isFabVisible = true;
  double _lastScrollPosition = 0;

  bool _previousAuthState = false;

  @override
  void initState() {
    super.initState();
    _communityViewModel = context.read<CommunityViewModel>();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fabAnimationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _communityViewModel.loadMoments(refresh: true);
      _previousAuthState = context.read<AuthViewModel>().isUserLoggedIn();
    });

    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_communityViewModel.isLoading && _communityViewModel.hasMore) {
        _communityViewModel.loadMoments();
      }
    }

    final currentPosition = _scrollController.position.pixels;
    if (currentPosition > _lastScrollPosition &&
        _isFabVisible &&
        currentPosition > 100) {
      _isFabVisible = false;
      _fabAnimationController.reverse();
    } else if (currentPosition < _lastScrollPosition && !_isFabVisible) {
      _isFabVisible = true;
      _fabAnimationController.forward();
    }
    _lastScrollPosition = currentPosition;
  }

  final List<String> _filterOptions = ["全部", "钓获分享", "装备展示", "技巧分享", "问答求助"];

  @override
  Widget build(BuildContext context) {
    final authViewModel = context.watch<AuthViewModel>();
    final currentAuthState = authViewModel.isUserLoggedIn();

    if (_previousAuthState != currentAuthState) {
      _previousAuthState = currentAuthState;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _communityViewModel.loadMoments(refresh: true);
        }
      });
    }

    final communityViewModel = context.watch<CommunityViewModel>();
    final isLoading = communityViewModel.isLoading;
    final moments = communityViewModel.moments;
    final hasMore = communityViewModel.hasMore;
    final hasSelectedTags = communityViewModel.selectedTags.isNotEmpty;

    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: RefreshIndicator(
        onRefresh: () => communityViewModel.loadMoments(refresh: true),
        child: CustomScrollView(
          controller: _scrollController,
          physics: const BouncingScrollPhysics(),
          slivers: [
            SliverAppBar(
              floating: true,
              backgroundColor: Colors.white,
              elevation: 0,
              automaticallyImplyLeading: false,
              title: const Text(
                '社区',
                style: TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w600,
                ),
              ),
              actions: [
                IconButton(
                  onPressed: _showSortOptions,
                  icon: Icon(
                    communityViewModel.sortBy == 'latest'
                        ? Icons.schedule
                        : Icons.local_fire_department,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                IconButton(
                  onPressed: () => context.push(AppRoutes.search),
                  icon: Icon(Icons.search, color: Colors.grey.shade700),
                ),
                IconButton(
                  onPressed: _showNotifications,
                  icon: Stack(
                    children: [
                      Icon(Icons.notifications_outlined,
                          color: Colors.grey.shade700),
                      // TODO: Replace with real logic e.g., notificationViewModel.hasUnread
                      if (true)
                        Positioned(
                          top: 8,
                          right: 8,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
            SliverPersistentHeader(
              pinned: true,
              delegate: _OptimizedFilterBarDelegate(
                minHeight: 48,
                maxHeight: hasSelectedTags ? 84 : 48,
                child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      Container(
                        height: 48,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: [
                            if (isAuthenticated())
                              GestureDetector(
                                onTap: () {
                                  HapticFeedback.selectionClick();
                                  // Call the correct ViewModel method
                                  final newValue =
                                      !communityViewModel.showOnlyFollowing;
                                  communityViewModel
                                      .setShowOnlyFollowing(newValue);
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 6),
                                  decoration: BoxDecoration(
                                    color: communityViewModel.showOnlyFollowing
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade100,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        communityViewModel.showOnlyFollowing
                                            ? Icons.people
                                            : Icons.people_outline,
                                        size: 16,
                                        color:
                                            communityViewModel.showOnlyFollowing
                                                ? Colors.white
                                                : Colors.grey.shade700,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '关注',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: communityViewModel
                                                  .showOnlyFollowing
                                              ? Colors.white
                                              : Colors.grey.shade700,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            if (isAuthenticated()) const SizedBox(width: 8),
                            Expanded(
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: _filterOptions.map((filter) {
                                    final isSelected =
                                        communityViewModel.selectedMomentType ==
                                            filter;
                                    return Padding(
                                      padding: const EdgeInsets.only(right: 8),
                                      child: GestureDetector(
                                        onTap: () {
                                          HapticFeedback.selectionClick();
                                          communityViewModel
                                              .setMomentTypeFilter(filter);
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16, vertical: 8),
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? Theme.of(context).primaryColor
                                                : Colors.transparent,
                                            borderRadius:
                                                BorderRadius.circular(20),
                                            border: Border.all(
                                              color: isSelected
                                                  ? Theme.of(context)
                                                      .primaryColor
                                                  : Colors.grey.shade300,
                                            ),
                                          ),
                                          child: Text(
                                            filter,
                                            style: TextStyle(
                                              color: isSelected
                                                  ? Colors.white
                                                  : Colors.grey.shade700,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            GestureDetector(
                              onTap: _showAdvancedFilters,
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: hasSelectedTags
                                      ? Theme.of(context)
                                          .primaryColor
                                          .withOpacity(0.1)
                                      : Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Badge(
                                  isLabelVisible: hasSelectedTags,
                                  label: Text(communityViewModel
                                      .selectedTags.length
                                      .toString()),
                                  child: Icon(
                                    Icons.filter_list,
                                    size: 20,
                                    color: hasSelectedTags
                                        ? Theme.of(context).primaryColor
                                        : Colors.grey.shade700,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (hasSelectedTags)
                        Container(
                          height: 36,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: ListView(
                            scrollDirection: Axis.horizontal,
                            children: communityViewModel.selectedTags
                                .map((tag) => Container(
                                      margin: const EdgeInsets.only(right: 8),
                                      child: Chip(
                                        label: Text(tag),
                                        deleteIcon:
                                            const Icon(Icons.close, size: 16),
                                        onDeleted: () {
                                          communityViewModel.toggleTag(tag);
                                        },
                                        backgroundColor: Theme.of(context)
                                            .primaryColor
                                            .withOpacity(0.1),
                                        deleteIconColor:
                                            Theme.of(context).primaryColor,
                                        labelStyle: TextStyle(
                                          color: Theme.of(context).primaryColor,
                                          fontSize: 12,
                                        ),
                                        padding: EdgeInsets.zero,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            if (isLoading && moments.isEmpty)
              SliverToBoxAdapter(
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            else
              _buildOptimizedMomentsList(moments, isLoading, hasMore),
          ],
        ),
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimationController,
        child: FloatingActionButton.extended(
          onPressed: _showPublishOptions,
          backgroundColor: Theme.of(context).primaryColor,
          icon: const Icon(Icons.edit),
          label: const Text('发布'),
        ),
      ),
    );
  }

  Widget _buildOptimizedMomentsList(
      List moments, bool isLoading, bool hasMore) {
    if (moments.isEmpty && !isLoading) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.bubble_chart_outlined,
                  size: 80, color: Colors.grey.shade300),
              const SizedBox(height: 16),
              Text(
                _communityViewModel.showOnlyFollowing ? '还没有关注的人发布动态' : '暂无动态',
                style: TextStyle(color: Colors.grey.shade600, fontSize: 16),
              ),
              const SizedBox(height: 8),
              Text(
                _communityViewModel.showOnlyFollowing
                    ? '去发现更多有趣的钓友吧'
                    : '成为第一个分享的人',
                style: TextStyle(color: Colors.grey.shade500, fontSize: 14),
              ),
              const SizedBox(height: 24),
              OutlinedButton.icon(
                onPressed: _communityViewModel.showOnlyFollowing
                    ? () => _communityViewModel.setShowOnlyFollowing(false)
                    : _showPublishOptions,
                icon: Icon(_communityViewModel.showOnlyFollowing
                    ? Icons.explore
                    : Icons.add),
                label: Text(
                    _communityViewModel.showOnlyFollowing ? '发现动态' : '发布动态'),
                style: OutlinedButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index == moments.length) {
            if (isLoading) {
              return const Padding(
                padding: EdgeInsets.symmetric(vertical: 24),
                child: Center(child: CircularProgressIndicator(strokeWidth: 2)),
              );
            } else if (!hasMore) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 24),
                child: Center(
                  child: Column(
                    children: [
                      Icon(Icons.check_circle_outline,
                          color: Colors.grey.shade400, size: 32),
                      const SizedBox(height: 8),
                      Text('已经到底了',
                          style: TextStyle(
                              color: Colors.grey.shade500, fontSize: 13)),
                    ],
                  ),
                ),
              );
            }
            return const SizedBox(height: 80);
          }
          return _buildOptimizedMomentCard(moments[index]);
        },
        childCount: moments.isEmpty ? 0 : moments.length + 1,
      ),
    );
  }

  Widget _buildOptimizedMomentCard(moment) {
    final bool isLiked = moment.liked ?? false;
    final bool hasImages = moment.images != null && moment.images!.isNotEmpty;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showMomentDetail(moment),
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => _showUserProfile(moment.publisher),
                    child: Hero(
                      tag: 'avatar_${moment.id}',
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          image: moment.publisher.avatarUrl != null
                              ? DecorationImage(
                                  image:
                                      NetworkImage(moment.publisher.avatarUrl!),
                                  fit: BoxFit.cover,
                                )
                              : null,
                          color: Colors.grey.shade200,
                        ),
                        child: moment.publisher.avatarUrl == null
                            ? Icon(Icons.person,
                                color: Colors.grey.shade400, size: 20)
                            : null,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              moment.publisher.name.isNotEmpty
                                  ? moment.publisher.name
                                  : '匿名用户',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w600, fontSize: 15),
                            ),
                            if (moment.momentType != null) ...[
                              const SizedBox(width: 8),
                              _buildMomentTypeTag(moment.momentType),
                            ],
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(
                              _formatTimeAgo(moment.createdAt),
                              style: TextStyle(
                                  fontSize: 12, color: Colors.grey.shade500),
                            ),
                            if (moment.fishingSpotName != null) ...[
                              const SizedBox(width: 8),
                              Text('·',
                                  style:
                                      TextStyle(color: Colors.grey.shade500)),
                              const SizedBox(width: 8),
                              Flexible(
                                child: GestureDetector(
                                  onTap: () => _navigateToFishingSpot(
                                      moment.fishingSpotId),
                                  child: Text(
                                    moment.fishingSpotName!,
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: Theme.of(context).primaryColor),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _showMoreOptions(moment),
                    icon: Icon(Icons.more_horiz, color: Colors.grey.shade600),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
            if (moment.content != null && moment.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: Text(
                  moment.content!,
                  style: const TextStyle(fontSize: 15, height: 1.5),
                  maxLines: 5,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            if (hasImages) _buildOptimizedImageSection(moment.images!),
            if (moment.tags != null && moment.tags!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
                child: Wrap(
                  spacing: 8,
                  children: moment.tags!
                      .map<Widget>((tag) => GestureDetector(
                            onTap: () {
                              // Call the correct ViewModel method
                              _communityViewModel.toggleTag(tag);
                            },
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                  fontSize: 13,
                                  color: Theme.of(context).primaryColor),
                            ),
                          ))
                      .toList(),
                ),
              ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  _buildLikeButton(
                    isLiked: isLiked,
                    count: moment.likeCount ?? 0,
                    onTap: () => _toggleLike(moment.id),
                  ),
                  const SizedBox(width: 24),
                  _buildInteractionItem(
                    icon: Icons.mode_comment_outlined,
                    count: moment.commentCount ?? 0,
                    onTap: () => _showMomentDetail(moment),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => _shareMoment(moment),
                    icon: const Icon(Icons.share_outlined, size: 20),
                    color: Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  const SizedBox(width: 16),
                  IconButton(
                    onPressed: () => _toggleBookmark(moment.id),
                    icon: Icon(
                      moment.bookmarked ?? false
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      size: 20,
                    ),
                    color: moment.bookmarked ?? false
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade600,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSortOptions() {
    final currentSortBy = context.read<CommunityViewModel>().sortBy;
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('排序方式',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('最新发布'),
                  trailing: currentSortBy == 'latest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('latest');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_fire_department),
                  title: const Text('最热门'),
                  subtitle: const Text('按互动量排序'),
                  trailing: currentSortBy == 'hottest'
                      ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('hottest');
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        final List<String> localSelectedTags =
            List.from(_communityViewModel.selectedTags);
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20))),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('标签筛选',
                            style: TextStyle(
                                fontSize: 18, fontWeight: FontWeight.w600)),
                        TextButton(
                          onPressed: () =>
                              setModalState(() => localSelectedTags.clear()),
                          child: const Text('清除'),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          '新手入门',
                          '路亚技巧',
                          '台钓',
                          '野钓',
                          '黑坑',
                          '饵料配方',
                          '装备推荐',
                          '钓点分享',
                          '冬季钓鱼',
                          '鲫鱼',
                          '鲤鱼',
                          '草鱼',
                          '鲢鳙',
                          '翘嘴',
                          '鲈鱼'
                        ].map((tag) {
                          final isSelected = localSelectedTags.contains(tag);
                          return FilterChip(
                            label: Text(tag),
                            selected: isSelected,
                            onSelected: (selected) {
                              setModalState(() {
                                if (selected) {
                                  localSelectedTags.add(tag);
                                } else {
                                  localSelectedTags.remove(tag);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          // Call the new method in the ViewModel
                          _communityViewModel
                              .setSelectedTags(localSelectedTags);
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                        ),
                        child:
                            const Text('应用筛选', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // All other UI helper methods (_buildOptimizedImageSection, _buildLikeButton, _showPublishOptions, etc.)
  // can remain as they are, since they were already mostly correct. For completeness, they are included below.

  // Widget _buildOptimizedImageSection(List images) {
  //   if (images.length == 1) {
  //     return Padding(
  //       padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
  //       child: ClipRRect(
  //         borderRadius: BorderRadius.circular(12),
  //         child: AspectRatio(
  //           aspectRatio: 16 / 9,
  //           child: Image.network(
  //             images[0].imageUrl ?? images[0],
  //             fit: BoxFit.cover,
  //             errorBuilder: (context, error, stackTrace) => Container(
  //                 color: Colors.grey.shade200,
  //                 child: Icon(Icons.broken_image, color: Colors.grey.shade400)),
  //           ),
  //         ),
  //       ),
  //     );
  //   }
  //   final displayCount = images.length > 4 ? 4 : images.length;
  //   return Padding(
  //     padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
  //     child: GridView.builder(
  //       shrinkWrap: true,
  //       physics: const NeverScrollableScrollPhysics(),
  //       gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
  //         crossAxisCount: 2,
  //         crossAxisSpacing: 8,
  //         mainAxisSpacing: 8,
  //         childAspectRatio: 1,
  //       ),
  //       itemCount: displayCount,
  //       itemBuilder: (context, index) {
  //         final isLast = index == displayCount - 1;
  //         final remainingCount = images.length - displayCount;
  //         return Stack(
  //           fit: StackFit.expand,
  //           children: [
  //             ClipRRect(
  //               borderRadius: BorderRadius.circular(8),
  //               child: Image.network(
  //                 images[index].imageUrl ?? images[index],
  //                 fit: BoxFit.cover,
  //                 errorBuilder: (context, error, stackTrace) => Container(
  //                     color: Colors.grey.shade200,
  //                     child: Icon(Icons.broken_image,
  //                         color: Colors.grey.shade400)),
  //               ),
  //             ),
  //             if (isLast && remainingCount > 0)
  //               Container(
  //                 decoration: BoxDecoration(
  //                     color: Colors.black.withOpacity(0.6),
  //                     borderRadius: BorderRadius.circular(8)),
  //                 child: Center(
  //                   child: Text('+$remainingCount',
  //                       style: const TextStyle(
  //                           color: Colors.white,
  //                           fontSize: 20,
  //                           fontWeight: FontWeight.w600)),
  //                 ),
  //               ),
  //           ],
  //         );
  //       },
  //     ),
  //   );
  // }

  Widget _buildOptimizedImageSection(List<MomentImageVo> images) {
    if (images.length == 1) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: AspectRatio(
            aspectRatio: 16 / 9,
            // FIX: Access the .imageUrl property of the MomentImageVo object
            child: Image.network(
              images[0].imageUrl,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey.shade200,
                  child: Icon(Icons.broken_image, color: Colors.grey.shade400)),
            ),
          ),
        ),
      );
    }
    final displayCount = images.length > 4 ? 4 : images.length;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1,
        ),
        itemCount: displayCount,
        itemBuilder: (context, index) {
          final isLast = index == displayCount - 1;
          final remainingCount = images.length - displayCount;
          return Stack(
            fit: StackFit.expand,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                // FIX: Access the .imageUrl property of the MomentImageVo object here as well
                child: Image.network(
                  images[index].imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey.shade200,
                      child: Icon(Icons.broken_image,
                          color: Colors.grey.shade400)),
                ),
              ),
              if (isLast && remainingCount > 0)
                Container(
                  decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(8)),
                  child: Center(
                    child: Text('+$remainingCount',
                        style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildLikeButton(
      {required bool isLiked,
      required int count,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        child: Row(
          children: [
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              transitionBuilder: (child, animation) =>
                  ScaleTransition(scale: animation, child: child),
              child: Icon(
                isLiked ? Icons.thumb_up : Icons.thumb_up_outlined,
                key: ValueKey(isLiked),
                size: 22,
                color: isLiked
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade700,
              ),
            ),
            if (count > 0) ...[
              const SizedBox(width: 4),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 200),
                child: Text(
                  count > 999 ? '999+' : count.toString(),
                  key: ValueKey(count),
                  style: TextStyle(
                    fontSize: 14,
                    color: isLiked
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionItem(
      {required IconData icon,
      required int count,
      Color? color,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Row(
        children: [
          Icon(icon, size: 22, color: color ?? Colors.grey.shade700),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Text(
              count > 999 ? '999+' : count.toString(),
              style: TextStyle(
                  fontSize: 14,
                  color: color ?? Colors.grey.shade700,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMomentTypeTag(String? momentType) {
    if (momentType == null) return const SizedBox.shrink();
    String displayText;
    IconData icon;
    Color color;
    switch (momentType) {
      case 'fishing_catch':
        displayText = '钓获';
        icon = Icons.catching_pokemon;
        color = Colors.green;
        break;
      case 'equipment':
        displayText = '装备';
        icon = Icons.construction;
        color = Colors.blue;
        break;
      case 'technique':
        displayText = '技巧';
        icon = Icons.lightbulb_outline;
        color = Colors.orange;
        break;
      case 'question':
        displayText = '求助';
        icon = Icons.help_outline;
        color = Colors.purple;
        break;
      default:
        displayText = '动态';
        icon = Icons.article_outlined;
        color = Colors.grey;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12)),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 3),
          Text(displayText,
              style: TextStyle(
                  fontSize: 11, color: color, fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  void _showPublishOptions() {
    if (!isAuthenticated()) {
      _showLoginDialog('发布动态');
      return;
    }
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('发布动态',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.set_meal,
                              label: '钓获分享',
                              color: Colors.green,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '钓获分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.backpack,
                              label: '装备展示',
                              color: Colors.blue,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '装备展示');
                              }),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.tips_and_updates,
                              label: '技巧分享',
                              color: Colors.orange,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '技巧分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.help_outline,
                              label: '问答求助',
                              color: Colors.purple,
                              onTap: () {
                                Navigator.pop(context);
                                context.push(AppRoutes.publishMoment,
                                    extra: '问答求助');
                              }),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPublishOption(
      {required IconData icon,
      required String label,
      required Color color,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16)),
            child: Icon(icon, color: color, size: 32),
          ),
          const SizedBox(height: 8),
          Text(label,
              style: TextStyle(fontSize: 13, color: Colors.grey.shade700)),
        ],
      ),
    );
  }

  void _showMoreOptions(moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bookmark_outline),
                  title: const Text('收藏动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _toggleBookmark(moment.id);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share_outlined),
                  title: const Text('分享动态'),
                  onTap: () {
                    Navigator.pop(context);
                    _shareMoment(moment);
                  },
                ),
                if (!_isOwnMoment(moment))
                  ListTile(
                    leading: const Icon(Icons.person_add_outlined),
                    title: Text(moment.followed ?? false ? '取消关注' : '关注作者'),
                    onTap: () {
                      Navigator.pop(context);
                      _toggleFollow(moment.publisher.id);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.flag_outlined),
                  title: const Text('举报'),
                  onTap: () {
                    Navigator.pop(context);
                    _reportMoment(moment);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showNotifications() {
    if (!isAuthenticated()) {
      _showLoginDialog('查看通知');
      return;
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2)),
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('通知',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                    TextButton(onPressed: () {}, child: const Text('全部已读')),
                  ],
                ),
              ),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.notifications_none,
                          size: 80, color: Colors.grey.shade300),
                      const SizedBox(height: 16),
                      Text('暂无新通知',
                          style: TextStyle(
                              color: Colors.grey.shade600, fontSize: 16)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showMomentDetail(moment) async {
    final result = await Navigator.of(context).push<MomentVo>(
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(
          momentId: moment.id,
          initialMoment: moment,
        ),
      ),
    );

    // If the moment was updated in the detail page, sync it back to the list
    if (result != null) {
      _communityViewModel.updateMoment(result);
    }
  }

  void _showUserProfile(user) {
    context.pushNamed('user_profile',
        pathParameters: {'userId': user.id.toString()}, extra: user);
  }

  void _toggleLike(int momentId) async {
    if (!isAuthenticated()) {
      _showLoginDialog('点赞动态');
      return;
    }
    try {
      await _communityViewModel.toggleLikeMoment(momentId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('点赞失败: $e'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2)),
        );
      }
    }
  }

  void _toggleFollow(int userId) {
    if (!isAuthenticated()) {
      _showLoginDialog('关注用户');
      return;
    }
    _communityViewModel.toggleFollowUser(userId);
  }

  void _toggleBookmark(int momentId) async {
    if (!isAuthenticated()) {
      _showLoginDialog('收藏动态');
      return;
    }
    try {
      await _communityViewModel.toggleBookmark(momentId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('收藏失败: $e'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2)),
        );
      }
    }
  }

  void _shareMoment(moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(2)),
                ),
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('分享动态',
                      style:
                          TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
                ),
                ListTile(
                  leading: const Icon(Icons.copy),
                  title: const Text('复制链接'),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                        content: Text('链接已复制到剪贴板'),
                        behavior: SnackBarBehavior.floating));
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.share),
                  title: const Text('分享到其他应用'),
                  onTap: () => Navigator.pop(context),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void _reportMoment(moment) {
    if (!isAuthenticated()) {
      _showLoginDialog('举报动态');
      return;
    }
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return FutureBuilder<List<String>>(
          future: _communityViewModel.getReportReasons(),
          builder: (context, snapshot) {
            final reasons = snapshot.data ??
                ['垃圾信息', '违法违规', '色情内容', '暴力内容', '虚假信息', '侵犯版权', '其他'];
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20))),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2)),
                  ),
                  const Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('举报动态',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w600)),
                  ),
                  Expanded(
                    child: ListView.builder(
                      itemCount: reasons.length,
                      itemBuilder: (context, index) {
                        final reason = reasons[index];
                        return ListTile(
                          title: Text(reason),
                          onTap: () async {
                            Navigator.pop(context);
                            try {
                              await _communityViewModel.reportMoment(
                                  moment.id, reason);
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                        content: Text('举报已提交，我们会尽快处理'),
                                        behavior: SnackBarBehavior.floating));
                              }
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                        content: Text('举报失败: $e'),
                                        behavior: SnackBarBehavior.floating));
                              }
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _navigateToFishingSpot(int? fishingSpotId) {
    if (fishingSpotId != null) {
      context.pushNamed('fishing_spot_detail',
          pathParameters: {'id': fishingSpotId.toString()});
    }
  }

  bool _isOwnMoment(moment) {
    final authViewModel = context.read<AuthViewModel>();
    return authViewModel.currentUser?.id == moment.publisher.id;
  }

  String _formatTimeAgo(String? createdAt) {
    if (createdAt == null) return '';
    try {
      final dateTime = DateTime.parse(createdAt);
      return DateTimeUtil.formatTime(dateTime);
    } catch (e) {
      return createdAt;
    }
  }

  void _showLoginDialog(String action) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Text('需要登录'),
          content: Text('您需要先登录才能$action'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                context.push(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8))),
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  bool isAuthenticated() {
    return context.read<AuthViewModel>().isUserLoggedIn();
  }
}

class _OptimizedFilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;
  final double maxHeight;

  _OptimizedFilterBarDelegate({
    required this.child,
    required this.minHeight,
    required this.maxHeight,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: overlapsContent ? 2 : 0,
      shadowColor: Colors.black.withOpacity(0.1),
      child: child,
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant _OptimizedFilterBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
