import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/models/moment/moment_vo.dart';

class SpotMomentCard extends StatelessWidget {
  final MomentVo moment;
  final VoidCallback? onTap;

  const SpotMomentCard({
    super.key,
    required this.moment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // User header - flat design
              Row(
                children: [
                  // Simple avatar
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(18),
                    ),
                    child: moment.publisher.avatarUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(18),
                            child: CachedNetworkImage(
                              imageUrl: moment.publisher.avatarUrl!,
                              fit: BoxFit.cover,
                              errorWidget: (context, url, error) => Icon(
                                Icons.person,
                                size: 20,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          )
                        : Icon(
                            Icons.person,
                            size: 20,
                            color: Theme.of(context).primaryColor,
                          ),
                  ),
                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Flexible(
                              child: Text(
                                moment.publisher.name,
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  height: 1.2,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (moment.tag != null &&
                                moment.tag!.isNotEmpty) ...[
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: _getFlatTagColor(moment.tag!)
                                      .withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  moment.tag!,
                                  style: TextStyle(
                                    color: _getFlatTagColor(moment.tag!),
                                    fontSize: 11,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          _formatTime(moment.createTime ?? DateTime.now()),
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Content
              if (moment.content != null && moment.content!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  moment.content!,
                  style: const TextStyle(
                    fontSize: 15,
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              // Images - flat grid
              if (moment.pictures != null && moment.pictures!.isNotEmpty) ...[
                const SizedBox(height: 12),
                _buildFlatImageGrid(moment.pictures!),
              ],

              const SizedBox(height: 12),

              // Interaction bar - flat design
              Row(
                children: [
                  _buildFlatStat(
                    icon: (moment.isLiked ?? false)
                        ? Icons.favorite
                        : Icons.favorite_border,
                    count: moment.numberOfLikes,
                    color: (moment.isLiked ?? false)
                        ? Colors.red
                        : Colors.grey.shade500,
                  ),
                  const SizedBox(width: 16),
                  _buildFlatStat(
                    icon: Icons.chat_bubble_outline,
                    count: moment.commentCount.toInt(),
                    color: Colors.grey.shade500,
                  ),
                  const Spacer(),
                  Icon(
                    Icons.share_outlined,
                    size: 18,
                    color: Colors.grey.shade500,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

// Helper methods for flat design
  Widget _buildFlatStat({
    required IconData icon,
    required int count,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 18, color: color),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 13,
            color: color,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildFlatImageGrid(List<String> images) {
    if (images.isEmpty) return const SizedBox.shrink();

    // Single image - full width with flat styling
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: CachedNetworkImage(
            imageUrl: images[0],
            fit: BoxFit.cover,
            placeholder: (context, url) => Container(
              color: Colors.grey.shade100,
              child: Icon(
                Icons.image_outlined,
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
            errorWidget: (context, url, error) => Container(
              color: Colors.grey.shade100,
              child: Icon(
                Icons.broken_image_outlined,
                size: 32,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ),
      );
    }

    // Multiple images - flat grid layout
    return SizedBox(
      height: 180,
      child: Row(
        children: [
          // First image takes larger space
          Expanded(
            flex: 2,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                bottomLeft: Radius.circular(8),
              ),
              child: CachedNetworkImage(
                imageUrl: images[0],
                fit: BoxFit.cover,
                height: 180,
                placeholder: (context, url) => Container(
                  color: Colors.grey.shade100,
                ),
                errorWidget: (context, url, error) => Container(
                  color: Colors.grey.shade100,
                  child: Icon(
                    Icons.broken_image_outlined,
                    color: Colors.grey.shade400,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 2),
          // Remaining images in a column
          if (images.length > 1)
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  for (int i = 1; i < images.length && i < 3; i++) ...[
                    if (i > 1) const SizedBox(height: 2),
                    Expanded(
                      child: Stack(
                        fit: StackFit.expand,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.only(
                              topRight: i == 1
                                  ? const Radius.circular(8)
                                  : Radius.zero,
                              bottomRight: i == images.length - 1 || i == 2
                                  ? const Radius.circular(8)
                                  : Radius.zero,
                            ),
                            child: CachedNetworkImage(
                              imageUrl: images[i],
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey.shade100,
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey.shade100,
                                child: Icon(
                                  Icons.broken_image_outlined,
                                  color: Colors.grey.shade400,
                                ),
                              ),
                            ),
                          ),
                          // "+N" overlay for more images
                          if (i == 2 && images.length > 3)
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.6),
                                borderRadius: const BorderRadius.only(
                                  bottomRight: Radius.circular(8),
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  '+${images.length - 3}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Color _getFlatTagColor(String tag) {
    switch (tag) {
      case '钓获分享':
        return Colors.green.shade600;
      case '装备展示':
        return Colors.blue.shade600;
      case '技巧分享':
        return Colors.orange.shade600;
      case '问答求助':
        return Colors.purple.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 30) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }
}
